import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/api.dart';
import 'package:mapp_prachakij_v3/component/url.dart';
import 'package:mapp_prachakij_v3/model/eventAndNews_model/eventAndNews.dart';
import 'package:mapp_prachakij_v3/model/eventAndNews_model/feelwell.dart';
import 'package:mapp_prachakij_v3/model/eventAndNews_model/new_and_event.dart';

class EventAndNewsController extends GetxController {

  var isLoading = true.obs;

  EventList eventList = EventList();
  FeelWellList feelWellList = FeelWellList();
  NewsList newsList = NewsList();

  RxInt indexEvent = 0.obs;
  RxInt indexNews = 0.obs;
  RxInt indexFeelWell = 0.obs;

  @override
  void onInit() async {
    super.onInit();
     getEvent();
     getFeelWell();
     getNews();
    update();
  }

  Future<dynamic> getEvent() async {
    try {
      isLoading(true);

      Map data = {};
      final responseEvent = await AppApi.callAPIjwt("GET", AppUrl.getEvent, data);
      if(responseEvent['status'] == 200){
        eventList = EventList.fromJson(responseEvent["result"]);
      } else {
        eventList = EventList(data: []);
      }
      update();
    }catch(e){
      if (kDebugMode) {
        print("error event getEvent =>$e");
      }
      // const GetSnackBar(
      //   title: "เกิดข้อผิดพลาด",
      //   message: "เกิดข้อผิดพลาด",
      //   duration: Duration(seconds: 3),
      // );
    }finally{
      isLoading(false);
    }
  }

  Future<dynamic> getFeelWell() async {
    try {
      final responseFeelWell = await AppApi.get(AppUrl.getFeelWellTVForApp);
      feelWellList = FeelWellList.fromJson(responseFeelWell["result"]);
      update();
    }catch(e){
      if (kDebugMode) {
        print("error getFeelWell =>$e");
      }
      // const GetSnackBar(
      //   title: "เกิดข้อผิดพลาด",
      //   message: "เกิดข้อผิดพลาด",
      //   duration: Duration(seconds: 3),
      // );
    }
  }

  Future<dynamic> getNews() async {
    try {
      final responseNews = await AppApi.callAPIjwt("GET", AppUrl.getNews, {});
      if(responseNews['status'] == 200){
        newsList = NewsList.fromJson(responseNews["result"]);
      } else {
        newsList = NewsList(data: []);
      }
      update();
    }catch(e){
      if (kDebugMode) {
        print("error getNews =>$e");
      }
      // const GetSnackBar(
      //   title: "เกิดข้อผิดพลาด",
      //   message: "เกิดข้อผิดพลาด",
      //   duration: Duration(seconds: 3),
      // );
    }
  }

}