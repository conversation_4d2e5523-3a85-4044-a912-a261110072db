import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mapp_prachakij_v3/component/secureStorage.dart';
import 'package:mapp_prachakij_v3/controller/psiController/psi_controller.dart';
import 'package:mapp_prachakij_v3/controller/responsive/mobile_body.dart';
import 'package:mapp_prachakij_v3/controller/service/service.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/eventAndNews_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/likepoint_controller/likepoint_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/login_controller/login_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/page_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/profile_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/promotion_controller.dart';
import 'package:mapp_prachakij_v3/controller/setting_controller/setting_controller.dart';
import 'package:mapp_prachakij_v3/intro.dart';
import 'package:mapp_prachakij_v3/view/home/<USER>';
import 'package:rive/rive.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_analytics/observer.dart';
import 'package:flutter/material.dart' as flutter;
import 'package:mapp_prachakij_v3/controller/tutorial_controller.dart';
import 'controller/ai_tutorial_controller.dart';
import 'dart:html' as html show window;
// ✅ เฉพาะ web เท่า
import 'dart:html' as html show document, CustomEvent;
import 'dart:async';
import 'dart:io' show TimeoutException;

class IndexPage extends StatefulWidget {
  const IndexPage({Key? key}) : super(key: key);
  @override
  State<IndexPage> createState() => _IndexPageState();
}

class _IndexPageState extends State<IndexPage> {
  final SecureStorage secureStorage = SecureStorage();
  static FirebaseAnalytics analytics = FirebaseAnalytics.instance;
  static FirebaseAnalyticsObserver observer =
      FirebaseAnalyticsObserver(analytics: analytics);

  String? uid;
  bool? loginStatus;
  bool? firstUse;
  bool isLoading = true;
  bool hasNavigated = false; // ✅ ป้องการ navigate ซ้ำ

  // Controllers
  final profileCtl = Get.put(ProfileController(), permanent: true);
  final loginCtl = Get.put(LoginController(), permanent: true);
  final pageCtl = Get.put(PageSelectController(), permanent: true);
  final centerCtl = Get.put(SettingController(), permanent: true);
  final likePointCtl = Get.put(LikePointController(), permanent: true);
  final eventCtl = Get.put(EventAndNewsController(), permanent: true);
  final promotionCtl = Get.put(PromotionController(), permanent: true);
  final psiCtl = Get.put(PsiController(), permanent: true);
  final tutorialCtl = Get.put(TutorialController(), permanent: true);
  final aiTutorialCtl = Get.put(AiTutorialController(), permanent: true);

  @override
  void initState() {
    super.initState();

    // ✅ Debug info
    if (kDebugMode) {
      print('🚀 IndexPage initState called');
      print('📱 Platform: ${kIsWeb ? 'Web' : 'Mobile'}');
    }

    // ซ่อน web loading� web
    if (kIsWeb) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _hideWebLoading();
      });
    }

    // เล่มโหลดข้อมูล
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeApp();
    });

    analytics.setCurrentScreen(screenName: "Index");
  }

  // ✅ ซ่อน web loading
  void _hideWebLoading() {
    if (kIsWeb) {
      try {
        final loadingElement = html.document.getElementById('loading');
        if (loadingElement != null) {
          loadingElement.style.opacity = '0';
          loadingElement.style.transition = 'opacity 0.5s ease-out';

          // ส่ง event ให้ JavaScript รู้ว่า Flutter พร้อมแล้ว
          html.window.dispatchEvent(html.CustomEvent('flutter-initialized'));

          Timer(const Duration(milliseconds: 500), () {
            loadingElement.style.display = 'none';
            if (kDebugMode) {
              print('✅ Web loading hidden with animation');
            }
          });
        }
      } catch (e) {
        if (kDebugMode) {
          print('❌ Cannot hide web loading: $e');
        }
      }
    }
  }

// ✅ เล่ม methodเช็ค Firebase
  bool _isRunningOnLocalhost() {
    if (kIsWeb) {
      try {
        final currentUrl = html.window.location.href;
        return currentUrl.contains('localhost') ||
            currentUrl.contains('127.0.0.1');
      } catch (e) {
        print('❌ Error checking localhost: $e');
      }
    }
    return false;
  }

  // ✅ โหลดข้อมูล login
  Future<void> getLogin() async {
    try {
      await SharedPreferences.getInstance();
      loginStatus = await AppService.getPref('bool', 'loginStatus');
      firstUse = await AppService.getPref('bool', 'firstUse');
    } catch (e) {
      if (kDebugMode) {
        print('Error getting login status: $e');
      }
      // ✅ ตั้งค่าเริ่มต้นถ้า error
      loginStatus = false;
      firstUse = true;
    }
  }

  // ✅ Safe API call with error handling
  Future<dynamic> _safeApiCall(
      Future<dynamic> Function() apiCall, String apiName) async {
    try {
      return await apiCall();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error in $apiName: $e');

        // ✅ ถ้ารันบน localhost และ error เ่ยว Firebase ให้แสดงข้อความ
        if (_isRunningOnLocalhost() && e.toString().contains('firebase')) {
          print(
              '🔄 Firebase error on localhost - this is expected and can be ignored');
        }
      }
      return null;
    }
  }

  // ✅ เล่มต้นแอพ
// ✅ เล่มต้นแอพ
  Future<void> _initializeApp() async {
    // ✅ ตรวจสอบ Production Mode
    final isProduction = kReleaseMode || const bool.fromEnvironment('dart.vm.product');

    if (isProduction) {
      // 🚀 Production Mode - ข้ามทุกอย่าง ไปหน้าแรกทันที
      print('🚀 PRODUCTION MODE - Fast startup');

      // ✅ ตั้งค่าเริ่มต้น
      loginStatus = true;
      firstUse = false;

      // ✅ Navigate ทันที
      _navigateToNextScreen();
      return;
    }

    // 🔧 Development Mode - โหลดปกติ
    print('🔧 DEVELOPMENT MODE - Full initialization');

    final initTimeout = Timer(const Duration(milliseconds: 10000), () {
      if (!hasNavigated && mounted) {
        print('⏰ Timeout - forcing navigation');
        _navigateToNextScreen();
      }
    });

    try {
      await getLogin();

      if (!Get.isRegistered<ProfileController>()) {
        Get.put(ProfileController(), permanent: true);
      }

      initTimeout.cancel();
      _navigateToNextScreen();
    } catch (e) {
      if (kDebugMode) {
        print('Error in initializeApp: $e');
      }
      // ✅ Cancel timeout และ navigate ต่อไป
      initTimeout.cancel();
      _navigateToNextScreen();
    }
  }

// ในไฟล์ lib/index.dart
  void _navigateToNextScreen() {
    if (hasNavigated || !mounted) return;

    hasNavigated = true;

    if (kDebugMode) {
      print(
          '🧭 Navigation: loginStatus=$loginStatus, firstUse=$firstUse, platform=${kIsWeb ? "Web" : "Mobile"}');
    }

    if (mounted) {
      setState(() {
        isLoading = false;
      });

      // ✅ รอให้ GetMaterialApp พร้อมก่อน navigate
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Future.delayed(const Duration(milliseconds: 2000), () {
          if (mounted && !hasNavigated) {
            try {
              // ✅ ซ่อน web loading ก่อน navigate
              _hideWebLoading();

              if (kIsWeb) {
                final uri = Uri.parse(html.window.location.href);

                // ✅ ใช้ Get.offAllNamed แต่ด้วย error handling
                if (uri.path.contains('/register')) {
                  if (kDebugMode) print('🧭 Web: Navigating to /register');
                  _safeNavigate('/register', parameters: uri.queryParameters);
                } else if (uri.path == '/login') {
                  if (kDebugMode) print('🧭 Web: Navigating to /login');
                  _safeNavigate('/login');
                } else if (loginStatus == true) {
                  if (kDebugMode) print('🧭 Web: Navigating to /home');
                  _safeNavigate('/home');
                } else {
                  if (kDebugMode) print('🧭 Web: Navigating to /login');
                  _safeNavigate('/login');
                }
              } else {
                // Mobile navigation
                if (loginStatus == false && firstUse == true) {
                  Get.offAll(() => const IntroPage());
                } else if (loginStatus == true) {
                  Get.offAll(() => const HomeNavigator());
                } else {
                  Get.offAll(() => const HomeNavigator());
                }
              }
            } catch (e) {
              if (kDebugMode) print('❌ Navigation error: $e');
              _handleNavigationError();
            }
          }
        });
      });
    }
  }

  // ✅ Safe navigation method
  void _safeNavigate(String route, {Map<String, String>? parameters}) {
    try {
      if (Get.context != null) {
        Get.offAllNamed(route, parameters: parameters);
      } else {
        // Fallback to browser navigation
        if (kIsWeb) {
          final url = parameters != null && parameters.isNotEmpty
              ? '$route?${parameters.entries.map((e) => '${e.key}=${e.value}').join('&')}'
              : route;
          html.window.location.href = url;
        }
      }
    } catch (e) {
      if (kDebugMode) print('❌ Safe navigation error: $e');
      _handleNavigationError();
    }
  }

  // ✅ Handle navigation errors
  void _handleNavigationError() {
    if (kIsWeb) {
      // Force reload with cache bust
      final currentURL = html.window.location.href.split('?')[0].split('#')[0];
      html.window.location.href =
          '$currentURL?v=${DateTime.now().millisecondsSinceEpoch}&error_recovery=1';
    }
  }

  @override
  void dispose() {
    // ✅ ป้องการ navigate ซ้ำเมื่อ dispose
    hasNavigated = true;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (kDebugMode) {
      print('🎨 IndexPage build called - isLoading: $isLoading');
    }
    return Scaffold(
      backgroundColor: const Color(0xFFFFB200),
      body: Stack(
        children: [
          // ✅ Background
          Container(
            color: const Color(0xFFFFB200),
          ),

          // ✅ Main content
          Center(
            child: Column(
              children: [
                // ✅ Logo
                Container(
                  margin: const EdgeInsets.only(top: 157),
                  child: flutter.Image.asset(
                    'assets/image/logo.png',
                    height: 163,
                    errorBuilder: (context, error, stackTrace) {
                      if (kDebugMode) {
                        print('Logo load error: $error');
                      }
                      return Container(
                        height: 163,
                        width: 163,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: const Icon(
                          Icons.image_not_supported,
                          color: Colors.red,
                          size: 50,
                        ),
                      );
                    },
                  ),
                ),

                // ✅ Rive Animation
                Container(
                  margin: const EdgeInsets.only(top: 140),
                  height: 118,
                  child: const RiveAnimation.asset(
                    'assets/animatedlogo/Chang-running.riv',
                    fit: BoxFit.contain,
                  ),
                ),

                const Spacer(),

                // ✅ Footer text
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: <Widget>[
                    Row(
                      children: const <Widget>[
                        Text(
                          'ISUZU',
                          style: TextStyle(
                            fontFamily: 'Prompt-Medium',
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w700,
                            letterSpacing: 1,
                          ),
                        ),
                        SizedBox(width: 5),
                        Text(
                          'PRACHAKIJ',
                          style: TextStyle(
                            fontFamily: 'Prompt',
                            color: Colors.white,
                            fontSize: 12,
                            letterSpacing: 1.5,
                          ),
                        )
                      ],
                    )
                  ],
                ),

                const SizedBox(height: 50)
              ],
            ),
          ),

          // ✅ Loading indicator  non-web
          if (isLoading && !kIsWeb)
            Container(
              color: Colors.black26,
              child: const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
