(function() {
  'use strict';

  console.log('🚨 Error Recovery System v2.1 Loading...');

  // ✅ Check if we're already in recovery mode
  const urlParams = new URLSearchParams(window.location.search);
  const isRecoveryMode = urlParams.has('force') || urlParams.has('clear') || urlParams.has('v');
  let recoveryAttempts = parseInt(sessionStorage.getItem('recoveryAttempts') || '0');

  if (isRecoveryMode) {
    recoveryAttempts++;
    sessionStorage.setItem('recoveryAttempts', recoveryAttempts.toString());
    console.log(`🔄 Recovery attempt #${recoveryAttempts}`);

    // ✅ Prevent infinite loops - max 3 attempts
    if (recoveryAttempts > 3) {
      console.log('🛑 Max recovery attempts reached, stopping');
      sessionStorage.removeItem('recoveryAttempts');
      // Clean URL without parameters
      const cleanURL = window.location.origin + window.location.pathname;
      window.history.replaceState({}, '', cleanURL);
      return;
    }
  }

  // ✅ Aggressive cache clearing (only if needed)
  function aggressiveCacheClear() {
    try {
      if (window.localStorage) {
        const essential = ['phone', 'loginStatus', 'firstUse'];
        const keys = Object.keys(localStorage);
        keys.forEach(key => {
          if (!essential.includes(key)) {
            localStorage.removeItem(key);
          }
        });
      }

      if (window.sessionStorage) {
        // Keep recovery attempts counter
        const attempts = sessionStorage.getItem('recoveryAttempts');
        sessionStorage.clear();
        if (attempts) {
          sessionStorage.setItem('recoveryAttempts', attempts);
        }
      }

      if ('caches' in window) {
        caches.keys().then(names => {
          names.forEach(name => caches.delete(name));
        });
      }

      console.log('✅ Cache clearing completed');
    } catch (e) {
      console.warn('Cache clearing failed:', e);
    }
  }

  // ✅ Smart reload - avoid infinite loops
  function smartReload() {
    if (recoveryAttempts >= 3) {
      console.log('🛑 Max attempts reached, not reloading');
      return;
    }

    aggressiveCacheClear();
    
    const baseURL = window.location.origin;
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(7);
    
    console.log(`🔄 Smart reload attempt ${recoveryAttempts + 1}`);
    
    try {
      window.location.replace(`${baseURL}/?v=${timestamp}&r=${randomId}&force=1`);
    } catch (e) {
      console.warn('Reload failed:', e);
      window.location.reload(true);
    }
  }

  // ✅ More specific error patterns (avoid false positives)
  const criticalErrorPatterns = [
    'Token generation',
    'contextless navigation',
    'GetMaterialApp',
    'Get.key',
    'ข้อพลาดในการสร้าง Token',
    'RenderFlex overflowed',
    'Failed to load main.dart.js'
  ];

  function hasCriticalError(text) {
    return criticalErrorPatterns.some(pattern => text.includes(pattern));
  }

  // ✅ Monitor DOM for critical errors only
  function monitorCriticalErrors() {
    const observer = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === Node.TEXT_NODE || node.nodeType === Node.ELEMENT_NODE) {
            const text = node.textContent || node.innerText || '';
            if (hasCriticalError(text)) {
              console.log('🚨 Critical error detected in DOM:', text.substring(0, 100));
              setTimeout(smartReload, 1000);
            }
          }
        });
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
      characterData: true
    });
  }

  // ✅ Enhanced error handlers (only for critical errors)
  window.addEventListener('error', function(e) {
    const message = e.message || e.error?.message || '';
    if (hasCriticalError(message)) {
      console.log('🚨 Critical window error detected:', message);
      setTimeout(smartReload, 500);
    }
  });

  window.addEventListener('unhandledrejection', function(e) {
    const reason = e.reason?.toString() || '';
    if (hasCriticalError(reason)) {
      console.log('🚨 Critical promise rejection detected:', reason);
      e.preventDefault();
      setTimeout(smartReload, 500);
    }
  });

  // ✅ Health check (less aggressive)
  function healthCheck() {
    // Only check if we're not already in recovery mode
    if (isRecoveryMode) return;

    const bodyText = document.body.textContent || '';
    if (hasCriticalError(bodyText)) {
      console.log('🚨 Critical error found in body');
      smartReload();
    }
  }

  // ✅ Initialize monitoring (only if not in recovery mode)
  function initialize() {
    if (isRecoveryMode) {
      console.log('🔄 In recovery mode, limited monitoring');
      // Clean URL after successful load
      setTimeout(() => {
        const cleanURL = window.location.origin + window.location.pathname;
        window.history.replaceState({}, '', cleanURL);
        sessionStorage.removeItem('recoveryAttempts');
      }, 10000); // เพิ่มเวลาให้แอพโหลดเสร็จ
      return;
    }

    // Start monitoring only for non-recovery loads
    setInterval(healthCheck, 15000); // ลดความถี่การตรวจสอบ

    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', monitorCriticalErrors);
    } else {
      monitorCriticalErrors();
    }

    // ✅ เพิ่มการตรวจสอบว่า Flutter โหลดเสร็จแล้วหรือยัง
    setTimeout(() => {
      if (document.body.textContent.includes('โหลด...') ||
          document.getElementById('loading')) {
        console.log('⚠️ Loading screen still visible after 30 seconds');
        // ไม่ reload ทันที ให้รออีกสักครู่
      }
    }, 30000);

    console.log('✅ Error Recovery System initialized (normal mode)');
  }

  // ✅ Start
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initialize);
  } else {
    initialize();
  }

  // ✅ Expose global recovery function
  window.forceAppReload = smartReload;

})();
