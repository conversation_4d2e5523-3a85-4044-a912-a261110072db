// API Timeout Fix Script
(function() {
  'use strict';
  
  console.log('🔧 API Timeout Fix Script Loading...');
  
  let apiTimeoutFixed = false;
  
  // ✅ ตรวจสอบว่าแอพติดที่การโหลด API หรือไม่
  function checkAPITimeout() {
    const bodyText = document.body.textContent || '';
    const isStuckOnAPI = bodyText.includes('Still waiting') || 
                        bodyText.includes('Get History') ||
                        bodyText.includes('Waiting for');
    
    const hasLoadingScreen = document.getElementById('loading') && 
                            document.getElementById('loading').style.display !== 'none';
    
    return {
      isStuckOnAPI,
      hasLoadingScreen,
      bodyText: bodyText.substring(0, 200)
    };
  }
  
  // ✅ บังคับให้แอพข้ามการโหลด API และไปหน้าถัดไป
  function forceSkipAPILoading() {
    if (apiTimeoutFixed) return;
    
    console.log('🔧 Forcing skip API loading...');
    apiTimeoutFixed = true;
    
    try {
      // ซ่อน loading screen
      const loadingElement = document.getElementById('loading');
      if (loadingElement) {
        loadingElement.style.display = 'none';
      }
      
      // ส่ง event ให้ Flutter รู้ว่าให้ข้ามการโหลด
      window.dispatchEvent(new CustomEvent('force-skip-loading'));
      
      // หากยังไม่ได้ผล ให้ reload หน้า
      setTimeout(() => {
        const status = checkAPITimeout();
        if (status.isStuckOnAPI) {
          console.log('🔄 Still stuck, reloading page...');
          window.location.reload();
        }
      }, 3000);
      
    } catch (e) {
      console.error('Error in forceSkipAPILoading:', e);
      // Fallback: reload page
      setTimeout(() => window.location.reload(), 1000);
    }
  }
  
  // ✅ เริ่มการตรวจสอบหลังจาก 8 วินาที (ลดลง)
  setTimeout(() => {
    console.log('🔍 Checking for API timeout issues...');

    const status = checkAPITimeout();
    console.log('API Status:', status);

    if (status.isStuckOnAPI) {
      console.log('⚠️ Detected API timeout issue - applying fix');
      forceSkipAPILoading();
    }
  }, 8000);
  
  // ✅ ตรวจสอบซ้ำทุก 5 วินาที สำหรับ 30 วินาที (เร็วขึ้น)
  let checkCount = 0;
  const maxChecks = 6; // 6 x 5 seconds = 30 seconds

  const intervalCheck = setInterval(() => {
    checkCount++;

    if (checkCount >= maxChecks) {
      clearInterval(intervalCheck);
      return;
    }

    const status = checkAPITimeout();
    if (status.isStuckOnAPI && !apiTimeoutFixed) {
      console.log(`⚠️ API timeout detected (check ${checkCount}/${maxChecks})`);
      forceSkipAPILoading();
      clearInterval(intervalCheck);
    }
  }, 5000);
  
  // ✅ ฟังก์ชันสำหรับ manual fix
  window.fixAPITimeout = function() {
    console.log('🔧 Manual API timeout fix triggered');
    forceSkipAPILoading();
  };
  
  // ✅ ฟังก์ชันสำหรับตรวจสอบสถานะ
  window.checkAPIStatus = checkAPITimeout;
  
  console.log('✅ API Timeout Fix Script initialized');
})();
