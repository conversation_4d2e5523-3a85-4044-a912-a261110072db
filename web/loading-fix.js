// Loading Fix Script for Production Issues
(function() {
  'use strict';
  
  console.log('🔧 Loading Fix Script v1.0 Loading...');
  
  let loadingFixAttempts = 0;
  const MAX_FIX_ATTEMPTS = 3;
  
  // ✅ ตรวจสอบสถานะการโหลด
  function checkLoadingStatus() {
    const loadingElement = document.getElementById('loading');
    const isLoadingVisible = loadingElement && 
      (loadingElement.style.display !== 'none' && 
       loadingElement.style.opacity !== '0');
    
    return {
      hasLoadingElement: !!loadingElement,
      isVisible: isLoadingVisible,
      hasFlutterApp: !!document.querySelector('flutter-view') || 
                     !!document.querySelector('flt-scene-host') ||
                     !!document.querySelector('[flt-renderer]'),
      bodyHasContent: document.body.children.length > 1
    };
  }
  
  // ✅ บังคับซ่อน loading screen
  function forceHideLoading() {
    const loadingElement = document.getElementById('loading');
    if (loadingElement) {
      console.log('🔧 Force hiding loading screen');
      loadingElement.style.opacity = '0';
      loadingElement.style.transition = 'opacity 0.5s ease-out';
      
      setTimeout(() => {
        loadingElement.style.display = 'none';
        console.log('✅ Loading screen forcefully hidden');
      }, 500);
      
      return true;
    }
    return false;
  }
  
  // ✅ ตรวจสอบและแก้ไขปัญหาการโหลด
  function performLoadingFix() {
    if (loadingFixAttempts >= MAX_FIX_ATTEMPTS) {
      console.log('🛑 Max loading fix attempts reached');
      return;
    }
    
    loadingFixAttempts++;
    console.log(`🔧 Loading fix attempt #${loadingFixAttempts}`);
    
    const status = checkLoadingStatus();
    console.log('📊 Loading status:', status);
    
    // ถ้ามี Flutter app แล้วแต่ loading ยังแสดงอยู่
    if (status.hasFlutterApp && status.isVisible) {
      console.log('🎯 Flutter app detected but loading still visible - fixing...');
      forceHideLoading();
      return;
    }
    
    // ถ้า body มีเนื้อหาแล้วแต่ loading ยังแสดงอยู่
    if (status.bodyHasContent && status.isVisible) {
      console.log('🎯 Body has content but loading still visible - fixing...');
      forceHideLoading();
      return;
    }
    
    // ถ้าไม่มี loading element แต่หน้าจอว่าง
    if (!status.hasLoadingElement && !status.hasFlutterApp) {
      console.log('⚠️ No loading element and no Flutter app - possible loading issue');
      // อาจจะต้อง reload หรือแสดง error message
    }
  }
  
  // ✅ ตรวจสอบทุก 5 วินาที
  function startLoadingMonitor() {
    const monitor = setInterval(() => {
      const status = checkLoadingStatus();
      
      // ถ้า loading หายไปแล้ว หยุดการตรวจสอบ
      if (!status.isVisible) {
        console.log('✅ Loading screen is hidden - stopping monitor');
        clearInterval(monitor);
        return;
      }
      
      // ถ้า loading ยังแสดงอยู่หลังจาก 30 วินาที
      if (status.isVisible) {
        console.log('⚠️ Loading screen still visible - attempting fix');
        performLoadingFix();
      }
    }, 5000);
    
    // หยุดการตรวจสอบหลังจาก 2 นาที
    setTimeout(() => {
      clearInterval(monitor);
      console.log('🛑 Loading monitor stopped after 2 minutes');
    }, 120000);
  }
  
  // ✅ เริ่มการตรวจสอบเมื่อหน้าโหลดเสร็จ
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(startLoadingMonitor, 10000); // รอ 10 วินาทีก่อนเริ่มตรวจสอบ
    });
  } else {
    setTimeout(startLoadingMonitor, 10000);
  }
  
  // ✅ ฟังก์ชันสำหรับ manual fix
  window.fixLoadingScreen = function() {
    console.log('🔧 Manual loading screen fix triggered');
    performLoadingFix();
  };
  
  // ✅ ฟังก์ชันสำหรับตรวจสอบสถานะ
  window.checkLoadingStatus = checkLoadingStatus;
  
  console.log('✅ Loading Fix Script initialized');
})();
